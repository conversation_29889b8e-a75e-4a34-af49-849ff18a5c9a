// ==UserScript==
// @name         雪球大王
// @namespace    https://yumeko.online/
// @version      4.0
// @description  在雪球股票页面显示员工分析数据，支持A股和港股，显示人均净利润、人均薪酬、员工人数等关键指标
// <AUTHOR>
// @match        https://xueqiu.com/S/*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @connect      lixinger.com
// ==/UserScript==

(function () {
  "use strict";

  let profitCard = null;
  let isDragging = false;
  let dragOffset = { x: 0, y: 0 };
  let cachedData = {}; // 数据缓存

  // 从URL获取股票代码
  function getStockSymbol() {
    const match = window.location.pathname.match(/\/S\/(.+)/);
    return match ? match[1] : null;
  }

  // 转换股票代码格式为lixingren格式
  function convertToLixingerCode(symbol) {
    if (symbol.startsWith("SZ")) {
      return "sz/" + symbol.substring(2);
    } else if (symbol.startsWith("SH")) {
      return "sh/" + symbol.substring(2);
    } else if (symbol.match(/^\d{5}$/) || symbol.match(/^\d{4}$/)) {
      // 港股：4-5位数字代码，如 09988, 0700
      return "hk/" + symbol;
    }
    return symbol.toLowerCase();
  }

  // 保存位置信息
  function savePosition(x, y) {
    try {
      if (typeof GM_setValue !== "undefined") {
        GM_setValue("profitCardPosition", JSON.stringify({ x, y }));
      } else {
        localStorage.setItem("profitCardPosition", JSON.stringify({ x, y }));
      }
    } catch (e) {
      console.log("无法保存位置信息:", e);
    }
  }

  // 保存缓存数据
  function saveCachedData(symbol, data) {
    try {
      const cacheKey = `employeeData_${symbol}`;
      const currentYear = new Date().getFullYear();
      const cacheData = {
        data: data,
        timestamp: Date.now(),
        cacheYear: currentYear, // 记录缓存的年份
        expiry: getNextYearTimestamp(), // 到下一年才过期
      };

      if (typeof GM_setValue !== "undefined") {
        GM_setValue(cacheKey, JSON.stringify(cacheData));
      } else {
        localStorage.setItem(cacheKey, JSON.stringify(cacheData));
      }
      console.log(`数据已缓存 (${currentYear}年):`, symbol);
    } catch (e) {
      console.log("无法保存缓存数据:", e);
    }
  }

  // 获取下一年1月1日的时间戳
  function getNextYearTimestamp() {
    const nextYear = new Date().getFullYear() + 1;
    return new Date(nextYear, 0, 1).getTime(); // 下一年1月1日
  }

  // 获取缓存数据
  function getCachedData(symbol) {
    try {
      const cacheKey = `employeeData_${symbol}`;
      let cached;

      if (typeof GM_getValue !== "undefined") {
        cached = GM_getValue(cacheKey, null);
      } else {
        cached = localStorage.getItem(cacheKey);
      }

      if (cached) {
        const cacheData = JSON.parse(cached);
        const currentYear = new Date().getFullYear();

        // 检查是否过期（到下一年才过期）
        if (Date.now() < cacheData.expiry) {
          console.log(`使用${cacheData.cacheYear}年缓存数据:`, symbol);
          // 标记数据来自缓存
          cacheData.data.fromCache = true;
          cacheData.data.cacheYear = cacheData.cacheYear;
          return cacheData.data;
        } else {
          console.log(
            `缓存数据已过期 (${cacheData.cacheYear}年 -> ${currentYear}年):`,
            symbol
          );
          // 清除过期缓存
          if (typeof GM_setValue !== "undefined") {
            GM_setValue(cacheKey, null);
          } else {
            localStorage.removeItem(cacheKey);
          }
        }
      }
    } catch (e) {
      console.log("无法获取缓存数据:", e);
    }
    return null;
  }

  // 获取保存的位置信息
  function getSavedPosition() {
    try {
      let saved;
      if (typeof GM_getValue !== "undefined") {
        saved = GM_getValue("profitCardPosition", null);
      } else {
        saved = localStorage.getItem("profitCardPosition");
      }
      return saved ? JSON.parse(saved) : { x: window.innerWidth - 450, y: 20 };
    } catch (e) {
      console.log("无法获取位置信息:", e);
      return { x: window.innerWidth - 450, y: 20 };
    }
  }

  // 创建浮动卡片
  function createEmployeeCard() {
    if (profitCard) {
      profitCard.remove();
    }

    const savedPos = getSavedPosition();

    profitCard = document.createElement("div");
    profitCard.id = "employee-analysis-card";
    profitCard.style.cssText = `
            position: fixed;
            left: ${savedPos.x}px;
            top: ${savedPos.y}px;
            width: 480px;
            max-height: 90vh;
            overflow-y: auto;
            background: linear-gradient(145deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.4), 0 8px 25px rgba(0,0,0,0.15);
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            color: white;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.15);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        `;

    // 添加样式
    if (!document.getElementById("employee-card-styles")) {
      const style = document.createElement("style");
      style.id = "employee-card-styles";
      style.textContent = `
                #employee-analysis-card:hover {
                    transform: translateY(-4px);
                    box-shadow: 0 25px 70px rgba(0,0,0,0.5), 0 12px 30px rgba(0,0,0,0.2);
                }
                .drag-handle {
                    cursor: move;
                    border-radius: 20px 20px 0 0;
                }
                .drag-handle:active {
                    cursor: grabbing;
                }
                .employee-table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 13px;
                    margin-bottom: 16px;
                    border-radius: 12px;
                    overflow: hidden;
                    background: rgba(255,255,255,0.05);
                }
                .employee-table th {
                    padding: 12px 8px;
                    text-align: center;
                    border-bottom: 2px solid rgba(255,255,255,0.2);
                    background: rgba(255,255,255,0.15);
                    font-weight: 600;
                    font-size: 12px;
                    letter-spacing: 0.5px;
                }
                .employee-table td {
                    padding: 10px 8px;
                    text-align: right;
                    border-bottom: 1px solid rgba(255,255,255,0.08);
                    transition: background-color 0.2s ease;
                }
                .employee-table tr:hover td {
                    background: rgba(255,255,255,0.08);
                }
                .employee-table td:first-child {
                    text-align: left;
                    font-weight: 600;
                    color: rgba(255,255,255,0.95);
                }
                .positive {
                    color: #4ade80;
                    font-weight: 600;
                }
                .negative {
                    color: #f87171;
                    font-weight: 600;
                }
                .currency-note {
                    font-size: 10px;
                    opacity: 0.7;
                    text-align: center;
                    margin-top: 8px;
                    padding: 6px 12px;
                    background: rgba(255,255,255,0.1);
                    border-radius: 8px;
                    border: 1px solid rgba(255,255,255,0.1);
                }
            `;
      document.head.appendChild(style);
    }

    // 创建卡片内容
    profitCard.innerHTML = `
            <div class="drag-handle" style="
                padding: 16px 20px;
                border-bottom: 1px solid rgba(255,255,255,0.2);
                display: flex;
                align-items: center;
                justify-content: space-between;
                background: rgba(255,255,255,0.1);
                border-radius: 16px 16px 0 0;
            ">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 22px;">👥</span>
                    <span style="font-weight: 600; font-size: 18px;">员工分析</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <button id="refresh-employee-data" style="
                        background: rgba(255,255,255,0.2);
                        border: 1px solid rgba(255,255,255,0.3);
                        color: white;
                        padding: 6px 12px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 12px;
                        opacity: 0.8;
                        transition: all 0.2s;
                    " onmouseover="this.style.opacity='1'; this.style.background='rgba(255,255,255,0.3)'"
                       onmouseout="this.style.opacity='0.8'; this.style.background='rgba(255,255,255,0.2)'">🔄 更新</button>
                    <button id="close-employee-card" style="
                        background: none;
                        border: none;
                        color: white;
                        font-size: 22px;
                        cursor: pointer;
                        opacity: 0.7;
                        transition: opacity 0.2s;
                    " onmouseover="this.style.opacity='1'" onmouseout="this.style.opacity='0.7'">×</button>
                </div>
            </div>
            <div style="padding: 20px;">
                <div id="employee-status" style="
                    text-align: center;
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 16px;
                    padding: 12px;
                    border-radius: 10px;
                    background: rgba(255,255,255,0.1);
                ">
                    正在获取数据...
                </div>
                <div id="employee-details" style="
                    font-size: 14px;
                    line-height: 1.5;
                ">
                    <!-- 详细数据将在这里显示 -->
                </div>
            </div>
        `;

    document.body.appendChild(profitCard);

    // 添加拖拽功能
    setupDragFunctionality();

    // 添加关闭功能
    document
      .getElementById("close-employee-card")
      .addEventListener("click", () => {
        profitCard.style.transform = "scale(0.8)";
        profitCard.style.opacity = "0";
        setTimeout(() => {
          if (profitCard) profitCard.remove();
          profitCard = null;
        }, 200);
      });

    // 添加手动更新功能
    document
      .getElementById("refresh-employee-data")
      .addEventListener("click", () => {
        const symbol = getStockSymbol();
        if (symbol) {
          console.log("手动更新数据:", symbol);
          fetchAndDisplayData(true); // 强制更新，忽略缓存
        }
      });

    return profitCard;
  }

  // 设置拖拽功能
  function setupDragFunctionality() {
    const dragHandle = profitCard.querySelector(".drag-handle");

    dragHandle.addEventListener("mousedown", startDrag);
    document.addEventListener("mousemove", drag);
    document.addEventListener("mouseup", stopDrag);

    function startDrag(e) {
      isDragging = true;
      const rect = profitCard.getBoundingClientRect();
      dragOffset.x = e.clientX - rect.left;
      dragOffset.y = e.clientY - rect.top;
      profitCard.style.transition = "none";
      dragHandle.style.cursor = "grabbing";
    }

    function drag(e) {
      if (!isDragging) return;

      const x = e.clientX - dragOffset.x;
      const y = e.clientY - dragOffset.y;

      // 限制在窗口范围内
      const maxX = window.innerWidth - profitCard.offsetWidth;
      const maxY = window.innerHeight - profitCard.offsetHeight;

      const constrainedX = Math.max(0, Math.min(x, maxX));
      const constrainedY = Math.max(0, Math.min(y, maxY));

      profitCard.style.left = constrainedX + "px";
      profitCard.style.top = constrainedY + "px";
    }

    function stopDrag() {
      if (isDragging) {
        isDragging = false;
        profitCard.style.transition = "transform 0.2s ease";
        dragHandle.style.cursor = "move";

        // 保存位置
        const rect = profitCard.getBoundingClientRect();
        savePosition(rect.left, rect.top);
      }
    }
  }

  // 格式化数字显示

  // 从lixingren获取员工数据
  function fetchLixingerEmployeeData(symbol) {
    return new Promise((resolve, reject) => {
      const lixingerCode = convertToLixingerCode(symbol);

      // 根据不同市场确定stockCode
      let stockCode;
      if (symbol.startsWith("SZ") || symbol.startsWith("SH")) {
        stockCode = symbol.substring(2); // A股：去掉SZ/SH前缀
      } else if (symbol.match(/^\d{4,5}$/)) {
        stockCode = symbol; // 港股：保持原始代码，支持4-5位数字
      } else {
        stockCode = symbol; // 其他情况保持原样
      }

      const url = `https://www.lixinger.com/equity/company/detail/${lixingerCode}/${stockCode}/employee`;

      GM_xmlhttpRequest({
        method: "GET",
        url: url,
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
          Referer: "https://www.lixinger.com/",
        },
        onload: function (response) {
          try {
            const html = response.responseText;
            const employeeData = parseLixingerEmployeeData(html);
            resolve(employeeData);
          } catch (e) {
            reject(e);
          }
        },
        onerror: function (error) {
          reject(error);
        },
      });
    });
  }

  // 解析lixingren员工数据
  function parseLixingerEmployeeData(html) {
    const data = {
      stockName: "",
      years: [],
      employeeCount: [],
      revenuePerEmployee: [],
      profitPerEmployee: [],
      salaryPerEmployee: [],
      currency: "CNY", // 默认人民币，港股会检测到HKD
    };

    try {
      // 提取股票名称
      const nameMatch = html.match(
        /<div[^>]*class="[^"]*fs-3[^"]*text-primary[^"]*"[^>]*>([^<]+)/
      );
      if (nameMatch) {
        data.stockName = nameMatch[1].trim();
      }

      // 提取指定的表格 - 使用table-header-gradient作为特征匹配（兼容A股和港股）
      let tableMatch = html.match(
        /<table[^>]*class="[^"]*table-header-gradient[^"]*"[^>]*>[\s\S]*?<\/table>/
      );

      if (!tableMatch) {
        console.log("未找到指定的表格");
        return data;
      }

      const tableHTML = tableMatch[0];
      console.log("找到目标表格，长度:", tableHTML.length);

      // 在表格内提取年份数据
      const yearMatches = tableHTML.match(
        /<span[^>]*class="[^"]*lxr-number-formatter[^"]*"[^>]*format="YYYY-MM-DD"[^>]*><span[^>]*><!---->(\d{4}-\d{2}-\d{2})<\/span>/g
      );
      if (yearMatches) {
        yearMatches.forEach((match) => {
          const yearMatch = match.match(/(\d{4})-\d{2}-\d{2}/);
          if (yearMatch) {
            data.years.push(parseInt(yearMatch[1]));
          }
        });
      }

      // 在表格内提取员工人数数据 - 支持A股"员工人数"和港股"员工总数"
      let employeeMatches = tableHTML.match(
        /<!--\[-->员工人数<!--\]-->[\s\S]*?(?=<\/tr>)/
      );
      let isHKStock = false;

      if (!employeeMatches) {
        // 尝试港股格式
        employeeMatches = tableHTML.match(/员工总数[\s\S]*?(?=<\/tr>)/);
        isHKStock = true;
      }

      if (employeeMatches) {
        console.log(
          `${isHKStock ? "港股员工总数" : "A股员工人数"}匹配结果:`,
          employeeMatches[0].substring(0, 300)
        );

        if (!isHKStock) {
          // A股新格式：<span class="lxr-number-formatter cmn_hans_cn"><span><!---->5,212</span></span>
          let numbers = employeeMatches[0].match(
            /<span[^>]*class="[^"]*lxr-number-formatter[^"]*cmn_hans_cn[^"]*"[^>]*><span[^>]*><!---->([0-9,]+)<\/span>/g
          );
          console.log("A股员工人数数字匹配:", numbers);
          if (numbers) {
            numbers.forEach((numMatch, index) => {
              const num = numMatch.match(/<!---->([0-9,]+)<\/span>/);
              if (num) {
                console.log(`A股解析第${index + 1}个员工人数:`, num[1]);
                data.employeeCount.push(parseInt(num[1].replace(/,/g, "")));
              }
            });
          }
        } else {
          // 港股格式：<!--[-->124,320.0000 <!----><!--]-->
          const hkNumbers = employeeMatches[0].match(
            /<!--\[-->([0-9,]+\.?[0-9]*) <!----><!--\]-->/g
          );
          console.log("港股员工总数匹配:", hkNumbers);
          if (hkNumbers) {
            hkNumbers.forEach((numMatch) => {
              const num = numMatch.match(
                /<!--\[-->([0-9,]+\.?[0-9]*) <!----><!--\]-->/
              );
              if (num) {
                console.log("解析员工总数:", num[1]);
                data.employeeCount.push(
                  parseInt(parseFloat(num[1].replace(/,/g, "")))
                );
              }
            });
          } else {
            // 更宽松的港股格式匹配
            const looseNumbers = employeeMatches[0].match(
              /<!--\[-->([0-9,]+\.?[0-9]*)/g
            );
            console.log("宽松港股员工总数匹配:", looseNumbers);
            if (looseNumbers) {
              looseNumbers.forEach((numMatch) => {
                const num = numMatch.match(/<!--\[-->([0-9,]+\.?[0-9]*)/);
                if (num) {
                  console.log("宽松解析员工总数:", num[1]);
                  data.employeeCount.push(
                    parseInt(parseFloat(num[1].replace(/,/g, "")))
                  );
                }
              });
            }
          }
        }
      }

      // 在表格内提取人均营业总收入数据
      const revenueMatches = tableHTML.match(
        /人均营业总收入[\s\S]*?(?=<\/tr>)/
      );
      if (revenueMatches) {
        // A股格式：支持人民币(CNY)和港币(HKD)
        let numbers = revenueMatches[0].match(
          /<!---->([0-9.-]+)万<\/span><span[^>]*class="currency[^"]*text-muted[^"]*(?:CNY|HKD)[^"]*"[^>]*>(?:元|港元)<\/span>/g
        );
        if (numbers) {
          numbers.forEach((numMatch) => {
            const num = numMatch.match(/<!---->([0-9.-]+)万<\/span>/);
            if (num) {
              data.revenuePerEmployee.push(parseFloat(num[1])); // 保持万元单位
            }
          });
        } else {
          // 港股格式：<!--[-->605.2939万元 <!----><!--]--> 或 <!--[-->605.2939万港币 <!----><!--]-->
          numbers = revenueMatches[0].match(
            /<!--\[-->([0-9.-]+)万(?:元|港币) <!---->/g
          );
          if (numbers) {
            numbers.forEach((numMatch) => {
              const num = numMatch.match(
                /<!--\[-->([0-9.-]+)万(?:元|港币) <!---->/
              );
              if (num) {
                data.revenuePerEmployee.push(parseFloat(num[1])); // 保持万元单位
                data.currency = "HKD"; // 港股数据
              }
            });
          }
        }
      }

      // 在表格内提取人均净利润数据
      const profitMatches = tableHTML.match(/人均净利润[\s\S]*?(?=<\/tr>)/);
      if (profitMatches) {
        // A股格式：支持人民币(CNY)和港币(HKD)
        let numbers = profitMatches[0].match(
          /<!---->(-?[0-9.-]+)万<\/span><span[^>]*class="currency[^"]*text-muted[^"]*(?:CNY|HKD)[^"]*"[^>]*>(?:元|港元)<\/span>/g
        );
        if (numbers) {
          numbers.forEach((numMatch) => {
            const num = numMatch.match(/<!---->(-?[0-9.-]+)万<\/span>/);
            if (num) {
              data.profitPerEmployee.push(parseFloat(num[1])); // 保持万元单位
            }

            // 检测货币类型
            if (numMatch.includes("HKD") || numMatch.includes("港元")) {
              data.currency = "HKD";
            }
          });
        } else {
          // 港股格式：<!--[-->76.5321万元 <!----><!--]--> 或 <!--[-->76.5321万港币 <!----><!--]-->
          numbers = profitMatches[0].match(
            /<!--\[-->(-?[0-9.-]+)万(?:元|港币) <!---->/g
          );
          if (numbers) {
            numbers.forEach((numMatch) => {
              const num = numMatch.match(
                /<!--\[-->(-?[0-9.-]+)万(?:元|港币) <!---->/
              );
              if (num) {
                data.profitPerEmployee.push(parseFloat(num[1])); // 保持万元单位
                data.currency = "HKD"; // 港股数据
              }
            });
          }
        }
      }

      // 在表格内提取人均薪酬数据
      const salaryMatches = tableHTML.match(/人均薪酬[\s\S]*?(?=<\/tr>)/);
      if (salaryMatches) {
        // A股格式：支持人民币(CNY)和港币(HKD)
        let numbers = salaryMatches[0].match(
          /<!---->([0-9.-]+)万<\/span><span[^>]*class="currency[^"]*text-muted[^"]*(?:CNY|HKD)[^"]*"[^>]*>(?:元|港元)<\/span>/g
        );
        if (numbers) {
          numbers.forEach((numMatch) => {
            const num = numMatch.match(/<!---->([0-9.-]+)万<\/span>/);
            if (num) {
              data.salaryPerEmployee.push(parseFloat(num[1])); // 保持万元单位
            }
          });
        } else {
          // 港股格式：<!--[-->XX.XX万元 <!----><!--]--> 或 <!--[-->XX.XX万港币 <!----><!--]-->
          numbers = salaryMatches[0].match(
            /<!--\[-->([0-9.-]+)万(?:元|港币) <!---->/g
          );
          if (numbers) {
            numbers.forEach((numMatch) => {
              const num = numMatch.match(
                /<!--\[-->([0-9.-]+)万(?:元|港币) <!---->/
              );
              if (num) {
                data.salaryPerEmployee.push(parseFloat(num[1])); // 保持万元单位
                data.currency = "HKD"; // 港股数据
              }
            });
          }
        }
      }
    } catch (error) {
      console.error("解析lixingren数据失败:", error);
    }

    return data;
  }

  // 更新员工分析卡片
  function updateEmployeeCard(employeeData) {
    if (!profitCard) return;

    const statusElement = profitCard.querySelector("#employee-status");
    const detailsElement = profitCard.querySelector("#employee-details");

    if (!employeeData || employeeData.years.length === 0) {
      statusElement.textContent = "数据获取失败";
      statusElement.style.background =
        "linear-gradient(135deg, #f44336, #d32f2f)";
      detailsElement.innerHTML = `
                <div style="text-align: center; opacity: 0.8;">
                    无法获取员工分析数据<br>
                    <small>请检查网络连接或稍后重试</small>
                </div>
            `;
      return;
    }

    // 更新状态
    statusElement.innerHTML = `
            <div>${employeeData.stockName || "当前股票"}</div>
            <div style="font-size: 14px; opacity: 0.9;">员工分析 (近${
              employeeData.years.length
            }年)</div>
        `;
    statusElement.style.background =
      "linear-gradient(135deg, #4CAF50, #45a049)";

    // 创建表格
    let tableHTML = `
            <div style="overflow-x: auto;">
                <table class="employee-table">
                    <thead>
                        <tr>
                            <th style="text-align: left;">指标</th>
        `;

    // 添加年份列
    employeeData.years.forEach((year) => {
      tableHTML += `<th>${year}</th>`;
    });

    tableHTML += `
                        </tr>
                    </thead>
                    <tbody>
        `;

    // 员工总数
    if (employeeData.employeeCount.length > 0) {
      tableHTML += `<tr><td>员工总数</td>`;
      employeeData.employeeCount.forEach((count) => {
        tableHTML += `<td>${count.toLocaleString()}人</td>`;
      });
      tableHTML += `</tr>`;
    }

    // 人均营业收入
    if (employeeData.revenuePerEmployee.length > 0) {
      tableHTML += `<tr><td>人均营业收入</td>`;
      employeeData.revenuePerEmployee.forEach((revenue) => {
        tableHTML += `<td>${revenue.toFixed(2)}万</td>`;
      });
      tableHTML += `</tr>`;
    }

    // 人均薪酬
    if (employeeData.salaryPerEmployee.length > 0) {
      tableHTML += `<tr><td>人均薪酬</td>`;
      employeeData.salaryPerEmployee.forEach((salary) => {
        tableHTML += `<td>${salary.toFixed(2)}万</td>`;
      });
      tableHTML += `</tr>`;
    }

    // 人均净利润（移到最后一栏）
    if (employeeData.profitPerEmployee.length > 0) {
      tableHTML += `<tr><td>人均净利润</td>`;
      employeeData.profitPerEmployee.forEach((profit) => {
        const className = profit >= 0 ? "positive" : "negative";
        tableHTML += `<td class="${className}">${profit.toFixed(2)}万</td>`;
      });
      tableHTML += `</tr>`;
    }

    tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

    // 添加货币单位备注和更新日期
    const currencyNote = employeeData.currency === "HKD" ? "港元" : "人民币";
    const updateDate = new Date().toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });

    // 显示数据来源状态和缓存年份
    let dataSource;
    if (employeeData.fromCache) {
      dataSource = `${employeeData.cacheYear}年缓存`;
    } else {
      dataSource = "最新数据";
    }

    tableHTML += `
            <div class="currency-note">
                单位：万${currencyNote} | ${dataSource} | ${updateDate}
            </div>
        `;

    detailsElement.innerHTML = tableHTML;
  }

  // 主要数据获取和处理函数
  async function fetchAndDisplayData(forceUpdate = false) {
    const symbol = getStockSymbol();
    if (!symbol) {
      console.log("无法获取股票代码");
      return;
    }

    console.log("正在获取股票数据:", symbol, forceUpdate ? "(强制更新)" : "");

    // 创建卡片
    if (!profitCard) {
      createEmployeeCard();
    }

    // 更新按钮状态
    const refreshBtn = document.getElementById("refresh-employee-data");
    if (refreshBtn) {
      refreshBtn.textContent = "⏳ 更新中...";
      refreshBtn.disabled = true;
      refreshBtn.style.opacity = "0.6";
    }

    try {
      let employeeData = null;

      // 如果不是强制更新，先尝试从缓存获取数据
      if (!forceUpdate) {
        employeeData = getCachedData(symbol);
      }

      // 如果没有缓存数据或强制更新，从网络获取
      if (!employeeData) {
        console.log("从网络获取数据...");
        employeeData = await fetchLixingerEmployeeData(symbol);

        // 保存到缓存
        if (employeeData && employeeData.years.length > 0) {
          saveCachedData(symbol, employeeData);
        }
      }

      console.log("员工数据:", employeeData);
      updateEmployeeCard(employeeData);
    } catch (error) {
      console.error("获取数据失败:", error);

      if (profitCard) {
        const statusElement = profitCard.querySelector("#employee-status");
        const detailsElement = profitCard.querySelector("#employee-details");

        statusElement.textContent = "数据获取失败";
        statusElement.style.background =
          "linear-gradient(135deg, #f44336, #d32f2f)";
        detailsElement.innerHTML = `
                    <div style="text-align: center; opacity: 0.8;">
                        请求失败，请稍后重试<br>
                        <small>错误: ${error.message || "网络错误"}</small>
                        <br><br>
                        <button onclick="fetchAndDisplayData(true)" style="
                            background: rgba(255,255,255,0.2);
                            border: 1px solid rgba(255,255,255,0.3);
                            color: white;
                            padding: 8px 16px;
                            border-radius: 6px;
                            cursor: pointer;
                        ">重新获取</button>
                    </div>
                `;
      }
    } finally {
      // 恢复按钮状态
      if (refreshBtn) {
        refreshBtn.textContent = "🔄 更新";
        refreshBtn.disabled = false;
        refreshBtn.style.opacity = "0.8";
      }
    }
  }

  // 等待页面加载完成
  function waitForPageLoad() {
    if (document.readyState === "complete") {
      setTimeout(fetchAndDisplayData, 1000);
    } else {
      window.addEventListener("load", () => {
        setTimeout(fetchAndDisplayData, 1000);
      });
    }
  }

  // 监听URL变化（适应SPA应用）
  let currentUrl = location.href;
  const observer = new MutationObserver(() => {
    if (location.href !== currentUrl) {
      currentUrl = location.href;
      if (currentUrl.includes("/S/")) {
        setTimeout(fetchAndDisplayData, 1500);
      }
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });

  // 初始化
  waitForPageLoad();
})();
